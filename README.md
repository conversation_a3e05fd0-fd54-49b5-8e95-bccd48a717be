# MiniTool1 - PyQt5 GUI Application

A PyQt5-based GUI application for firmware file processing with cutting, padding, DLL checking, and CRC checking capabilities.

## Features

### Main Interface
- **3 Tabs**: Cutting/Padding, DLL Checking, CRC Checking
- **File Selection**: Support for .hex, .bin, and .srec firmware files
- **Interactive Table**: Displays file information with resizable columns
- **Console Log**: Real-time logging of operations
- **Note Section**: For user annotations

### Cutting/Padding Tab
- **Software File Selection**: Browse and select firmware files
- **File Information Table**: 
  - File Name
  - Start Address
  - DLC (Data Length Code)
  - CRC (Cyclic Redundancy Check)
- **Operations**:
  - Default Cut
  - Padding
  - Load Cut Configuration
  - **Plus (+)**: Add dynamic command rows with:
    - Start Address input field
    - DLC value input field
    - Check box option
    - Padding value input field
    - Cut button (updates timestamp with current time)
    - Delete button (removes the command row)
    - Timestamp display
  - Custom Cut
  - Save Configuration
  - Convert
- **Multiple Files Chosen Section**: Scrollable area displaying command rows added via plus button
- **Console Log**: Separate section showing operation history and file selection status
- **Notes**: Text area for user notes

### Additional Tabs
- **DLL Checking**: Placeholder for DLL validation functionality
- **CRC Checking**: Placeholder for CRC validation functionality

## Requirements

- Python 3.6+
- PyQt5

## Installation

1. Install PyQt5:
```bash
pip install PyQt5
```

2. Run the application:
```bash
python MiniTool1.py
```

## Usage

1. **Select a File**: Click the "📁 Software file" button to browse and select a firmware file (.hex, .bin, or .srec)
2. **View File Information**: Selected files will appear in the table with their properties
3. **Add Command Rows**: Click the "+" button to add dynamic command rows in the "Multiple files chosen" section (requires files to be selected first)
4. **Scroll Through Commands**: The command rows area is scrollable when you add multiple rows
5. **Configure Commands**: In each command row, set:
   - Start Address
   - DLC value
   - Check box state
   - Padding value
6. **Execute Commands**: Click "Cut" to execute and timestamp the operation
7. **Remove Commands**: Click "Delete" to remove unwanted command rows
8. **Monitor Progress**: Check the separate console log section for operation status and messages
9. **Add Notes**: Use the note section to add comments or observations

## File Structure

```
MiniTool1/
├── MiniTool1.py          # Main application file
├── test_minitool.py      # Test script
└── README.md             # This file
```

## Testing

Run the test script to verify the application:

```bash
python test_minitool.py
```

## Current Status

- ✅ GUI Layout Complete
- ✅ File Selection Working
- ✅ Table Display Functional
- ✅ Console Logging Active
- ✅ Plus Button Dynamic Command Rows
- ✅ Cut Button with Timestamp Functionality
- ✅ Delete Command Row Functionality
- ✅ Input Validation (No files warning)
- ⏳ Business Logic (Other buttons show info messages)
- ⏳ DLL Checking Implementation
- ⏳ CRC Checking Implementation

## Future Enhancements

- Implement actual cutting and padding algorithms
- Add DLL checking functionality
- Add CRC validation and calculation
- File format parsing for .hex, .bin, and .srec files
- Configuration file save/load functionality
- Export capabilities

## License

This project is provided as-is for educational and development purposes.
