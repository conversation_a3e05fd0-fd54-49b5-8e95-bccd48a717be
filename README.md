# MiniTool1 - PyQt5 GUI Application

A PyQt5-based GUI application for firmware file processing with cutting, padding, DLL checking, and CRC checking capabilities.

## Features

### Main Interface
- **3 Tabs**: Cutting/Padding, DLL Checking, CRC Checking
- **File Selection**: Support for .hex, .bin, and .srec firmware files
- **Interactive Table**: Displays file information with resizable columns
- **Console Log**: Real-time logging of operations
- **Note Section**: For user annotations

### Cutting/Padding Tab
- **Software File Selection**: Browse and select firmware files
- **File Information Table**: 
  - File Name
  - Start Address
  - DLC (Data Length Code)
  - CRC (Cyclic Redundancy Check)
- **Operations**:
  - Default Cut
  - Padding
  - Load Cut Configuration
  - Custom Cut
  - Save Configuration
  - Convert
- **Console Log**: Shows operation history and file selection status
- **Notes**: Text area for user notes

### Additional Tabs
- **DLL Checking**: Placeholder for DLL validation functionality
- **CRC Checking**: Placeholder for CRC validation functionality

## Requirements

- Python 3.6+
- PyQt5

## Installation

1. Install PyQt5:
```bash
pip install PyQt5
```

2. Run the application:
```bash
python MiniTool1.py
```

## Usage

1. **Select a File**: Click the "📁 Software file" button to browse and select a firmware file (.hex, .bin, or .srec)
2. **View File Information**: Selected files will appear in the table with their properties
3. **Perform Operations**: Use the various buttons to perform cutting, padding, or conversion operations
4. **Monitor Progress**: Check the console log for operation status and messages
5. **Add Notes**: Use the note section to add comments or observations

## File Structure

```
MiniTool1/
├── MiniTool1.py          # Main application file
├── test_minitool.py      # Test script
└── README.md             # This file
```

## Testing

Run the test script to verify the application:

```bash
python test_minitool.py
```

## Current Status

- ✅ GUI Layout Complete
- ✅ File Selection Working
- ✅ Table Display Functional
- ✅ Console Logging Active
- ⏳ Business Logic (Placeholder - buttons show info messages)
- ⏳ DLL Checking Implementation
- ⏳ CRC Checking Implementation

## Future Enhancements

- Implement actual cutting and padding algorithms
- Add DLL checking functionality
- Add CRC validation and calculation
- File format parsing for .hex, .bin, and .srec files
- Configuration file save/load functionality
- Export capabilities

## License

This project is provided as-is for educational and development purposes.
