#!/usr/bin/env python3
"""
Test script for MiniTool1 GUI application
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

# Add the current directory to the path so we can import MiniTool1
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from MiniTool1 import MiniTool1


def test_application_startup():
    """Test that the application starts without errors"""
    app = QApplication(sys.argv)
    window = MiniTool1()

    # Test that the window was created
    assert window is not None
    assert window.windowTitle() == "MiniTool1"

    # Test that tabs were created
    assert window.tab_widget.count() == 3
    assert window.tab_widget.tabText(0) == "Cutting/Padding"
    assert window.tab_widget.tabText(1) == "DLL Checking"
    assert window.tab_widget.tabText(2) == "CRC Checking"

    # Test that the table has correct headers
    expected_headers = ["File Name", "Start Address", "DLC", "CRC"]
    for i, header in enumerate(expected_headers):
        assert window.file_table.horizontalHeaderItem(i).text() == header

    # Test plus button functionality with no files
    initial_row_count = window.command_rows_layout.count()
    window.plus_action()  # Should show warning message
    assert window.command_rows_layout.count() == initial_row_count  # No new rows added

    # Add a test file to the table
    window.add_file_to_table("test_file.hex")
    assert window.file_table.rowCount() == 1

    # Test plus button functionality with files
    window.plus_action()  # Should add a command row
    assert window.command_rows_layout.count() == initial_row_count + 1

    print("✅ All tests passed!")
    print("✅ Plus button functionality working correctly!")

    # Show the window briefly for visual verification
    window.show()
    QTest.qWait(2000)  # Wait 2 seconds

    app.quit()


if __name__ == "__main__":
    test_application_startup()
