import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget,
                             QVBoxLayout, QHBoxLayout, QPushButton,
                             QLineEdit, QTableWidget, QTableWidgetItem, QTextEdit,
                             QFileDialog, QLabel, QHeaderView, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class MiniTool1(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("MiniTool1")
        self.setGeometry(100, 100, 1000, 700)

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs
        self.cutting_padding_tab = self.create_cutting_padding_tab()
        self.dll_checking_tab = self.create_dll_checking_tab()
        self.crc_checking_tab = self.create_crc_checking_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.cutting_padding_tab, "Cutting/Padding")
        self.tab_widget.addTab(self.dll_checking_tab, "DLL Checking")
        self.tab_widget.addTab(self.crc_checking_tab, "CRC Checking")

        # Set main layout
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.tab_widget)
        central_widget.setLayout(main_layout)

    def create_cutting_padding_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()

        # Top section with file selection
        top_layout = QHBoxLayout()

        # Software file input
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("path/to/software file")
        self.file_path_input.setMinimumHeight(30)

        self.software_file_btn = QPushButton("📁 Software file")
        self.software_file_btn.setMinimumHeight(30)
        self.software_file_btn.clicked.connect(self.select_software_file)

        # Warning icon (placeholder)
        warning_label = QLabel("⚠️")
        warning_label.setStyleSheet("color: red; font-size: 16px;")

        top_layout.addWidget(self.file_path_input, 3)
        top_layout.addWidget(self.software_file_btn, 1)
        top_layout.addWidget(warning_label)

        # Console section
        console_layout = QHBoxLayout()

        # Left side - table and buttons
        left_layout = QVBoxLayout()

        # Table
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(4)
        self.file_table.setHorizontalHeaderLabels(["File Name", "Start Address", "DLC", "CRC"])

        # Make table headers resizable
        header = self.file_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setStretchLastSection(True)

        # Set minimum column widths
        self.file_table.setColumnWidth(0, 200)
        self.file_table.setColumnWidth(1, 120)
        self.file_table.setColumnWidth(2, 80)
        self.file_table.setColumnWidth(3, 80)

        left_layout.addWidget(self.file_table)

        # Middle buttons section
        middle_buttons_layout = QHBoxLayout()

        self.default_cut_btn = QPushButton("Default cut")
        self.default_cut_btn.clicked.connect(self.default_cut_action)

        self.padding_btn = QPushButton("Padding")
        self.padding_btn.clicked.connect(self.padding_action)

        self.load_config_btn = QPushButton("Load cut configuration")
        self.load_config_btn.clicked.connect(self.load_cut_configuration)

        self.plus_btn = QPushButton("+")
        self.plus_btn.setMaximumWidth(40)
        self.plus_btn.clicked.connect(self.plus_action)

        middle_buttons_layout.addWidget(self.default_cut_btn)
        middle_buttons_layout.addWidget(self.padding_btn)
        middle_buttons_layout.addStretch()
        middle_buttons_layout.addWidget(self.load_config_btn)
        middle_buttons_layout.addWidget(self.plus_btn)

        left_layout.addLayout(middle_buttons_layout)

        # Multiple files chosen section (Command rows area)
        multiple_files_label = QLabel("Multiple files chosen")
        multiple_files_label.setFont(QFont("Arial", 10, QFont.Bold))
        left_layout.addWidget(multiple_files_label)

        # Scrollable area for command rows
        from PyQt5.QtWidgets import QScrollArea
        self.command_scroll_area = QScrollArea()
        self.command_scroll_area.setMaximumHeight(200)
        self.command_scroll_area.setWidgetResizable(True)
        self.command_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.command_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.command_scroll_area.setStyleSheet("background-color: white; border: 1px solid #ccc;")

        # Widget to contain command rows
        self.command_rows_widget = QWidget()
        self.command_rows_layout = QVBoxLayout()
        self.command_rows_layout.setContentsMargins(5, 5, 5, 5)
        self.command_rows_widget.setLayout(self.command_rows_layout)

        # Add stretch to push command rows to top
        self.command_rows_layout.addStretch()

        self.command_scroll_area.setWidget(self.command_rows_widget)
        left_layout.addWidget(self.command_scroll_area)

        # Console log area (separate from command rows)
        console_label = QLabel("Console Log")
        console_label.setFont(QFont("Arial", 10, QFont.Bold))
        left_layout.addWidget(console_label)

        self.console_log = QTextEdit()
        self.console_log.setMaximumHeight(100)
        self.console_log.setPlaceholderText("Action logs will appear here...")
        self.console_log.append("Application started")

        left_layout.addWidget(self.console_log)

        # Bottom buttons
        bottom_buttons_layout = QHBoxLayout()

        self.custom_cut_btn = QPushButton("Custom cut")
        self.custom_cut_btn.clicked.connect(self.custom_cut_action)

        self.save_config_btn = QPushButton("Save Config")
        self.save_config_btn.clicked.connect(self.save_config_action)

        self.convert_btn = QPushButton("Convert")
        self.convert_btn.clicked.connect(self.convert_action)

        bottom_buttons_layout.addWidget(self.custom_cut_btn)
        bottom_buttons_layout.addWidget(self.save_config_btn)
        bottom_buttons_layout.addStretch()
        bottom_buttons_layout.addWidget(self.convert_btn)

        left_layout.addLayout(bottom_buttons_layout)

        # Right side - Note section
        right_layout = QVBoxLayout()
        note_label = QLabel("Note")
        note_label.setFont(QFont("Arial", 10, QFont.Bold))

        self.note_text = QTextEdit()
        self.note_text.setMaximumWidth(200)
        self.note_text.setPlaceholderText("Enter notes here...")

        right_layout.addWidget(note_label)
        right_layout.addWidget(self.note_text)

        # Combine left and right layouts
        console_layout.addLayout(left_layout, 4)
        console_layout.addLayout(right_layout, 1)

        # Add all sections to main tab layout
        layout.addLayout(top_layout)
        layout.addLayout(console_layout)

        tab.setLayout(layout)
        return tab

    def create_dll_checking_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()

        # Placeholder content for DLL Checking tab
        label = QLabel("DLL Checking Tab")
        label.setAlignment(Qt.AlignCenter)
        label.setFont(QFont("Arial", 16))

        layout.addWidget(label)
        tab.setLayout(layout)
        return tab

    def create_crc_checking_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()

        # Placeholder content for CRC Checking tab
        label = QLabel("CRC Checking Tab")
        label.setAlignment(Qt.AlignCenter)
        label.setFont(QFont("Arial", 16))

        layout.addWidget(label)
        tab.setLayout(layout)
        return tab

    # Button action methods
    def select_software_file(self):
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "Select Software File",
            "",
            "Firmware Files (*.hex *.bin *.srec);;All Files (*)"
        )

        if file_path:
            self.file_path_input.setText(file_path)
            self.add_file_to_table(file_path)
            self.console_log.append(f"Selected file: {os.path.basename(file_path)}")

    def add_file_to_table(self, file_path):
        row_count = self.file_table.rowCount()
        self.file_table.insertRow(row_count)

        # Add file name
        file_name = os.path.basename(file_path)
        self.file_table.setItem(row_count, 0, QTableWidgetItem(file_name))

        # Add placeholder values for other columns
        self.file_table.setItem(row_count, 1, QTableWidgetItem("0x00000000"))
        self.file_table.setItem(row_count, 2, QTableWidgetItem("0"))
        self.file_table.setItem(row_count, 3, QTableWidgetItem("0x0000"))

    def default_cut_action(self):
        self.console_log.append("Default cut action triggered")
        QMessageBox.information(self, "Info", "Default cut action - Not implemented yet")

    def padding_action(self):
        self.console_log.append("Padding action triggered")
        QMessageBox.information(self, "Info", "Padding action - Not implemented yet")

    def load_cut_configuration(self):
        self.console_log.append("Load cut configuration triggered")
        QMessageBox.information(self, "Info", "Load cut configuration - Not implemented yet")

    def plus_action(self):
        # Check if any files are selected
        if self.file_table.rowCount() == 0:
            QMessageBox.warning(self, "Warning", "No files chosen yet")
            return

        self.console_log.append("Adding new command row")
        self.add_command_row()

    def custom_cut_action(self):
        self.console_log.append("Custom cut action triggered")
        QMessageBox.information(self, "Info", "Custom cut action - Not implemented yet")

    def save_config_action(self):
        self.console_log.append("Save config action triggered")
        QMessageBox.information(self, "Info", "Save config action - Not implemented yet")

    def convert_action(self):
        self.console_log.append("Convert action triggered")
        QMessageBox.information(self, "Info", "Convert action - Not implemented yet")

    def add_command_row(self):
        """Add a new command row with input fields and buttons"""
        row_widget = QWidget()
        row_widget.setStyleSheet("background-color: #f8f8f8; border: 1px solid #ddd; margin: 2px; border-radius: 5px;")
        row_layout = QHBoxLayout()
        row_layout.setContentsMargins(8, 8, 8, 8)

        # Start Address input
        start_address_input = QLineEdit()
        start_address_input.setPlaceholderText("Start Address")
        start_address_input.setFixedWidth(100)
        start_address_input.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")

        # DLC value input
        dlc_input = QLineEdit()
        dlc_input.setPlaceholderText("DLC value")
        dlc_input.setFixedWidth(80)
        dlc_input.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")

        # Check box
        checkbox = QCheckBox("Check box")
        checkbox.setFixedWidth(80)

        # Padding value input
        padding_input = QLineEdit()
        padding_input.setPlaceholderText("padding value")
        padding_input.setFixedWidth(100)
        padding_input.setStyleSheet("padding: 5px; border: 1px solid #ccc; border-radius: 3px;")

        # Cut button
        cut_btn = QPushButton("Cut")
        cut_btn.setFixedWidth(60)
        cut_btn.setFixedHeight(30)
        cut_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 15px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        # Delete button
        delete_btn = QPushButton("✕ Delete")
        delete_btn.setFixedWidth(70)
        delete_btn.setFixedHeight(30)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border-radius: 15px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)

        # Timestamp label
        timestamp_label = QLabel("TimeStamp")
        timestamp_label.setFixedWidth(80)
        timestamp_label.setStyleSheet("""
            background-color: #e0e0e0;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
            text-align: center;
        """)
        timestamp_label.setAlignment(Qt.AlignCenter)

        # Add widgets to layout
        row_layout.addWidget(start_address_input)
        row_layout.addWidget(dlc_input)
        row_layout.addWidget(checkbox)
        row_layout.addWidget(padding_input)
        row_layout.addWidget(cut_btn)
        row_layout.addWidget(delete_btn)
        row_layout.addWidget(timestamp_label)
        row_layout.addStretch()

        row_widget.setLayout(row_layout)

        # Connect button actions
        cut_btn.clicked.connect(lambda: self.cut_action(timestamp_label, start_address_input, dlc_input, checkbox, padding_input))
        delete_btn.clicked.connect(lambda: self.delete_command_row(row_widget))

        # Remove the stretch before adding new row, then add stretch back
        self.command_rows_layout.takeAt(self.command_rows_layout.count() - 1)
        self.command_rows_layout.addWidget(row_widget)
        self.command_rows_layout.addStretch()

        # Store reference to the row widget for deletion
        if not hasattr(self, 'command_row_widgets'):
            self.command_row_widgets = []
        self.command_row_widgets.append(row_widget)

        # Scroll to bottom to show new row
        self.command_scroll_area.verticalScrollBar().setValue(
            self.command_scroll_area.verticalScrollBar().maximum()
        )

    def cut_action(self, timestamp_label, start_address_input, dlc_input, checkbox, padding_input):
        """Handle cut button action and update timestamp"""
        current_time = datetime.now().strftime("%H:%M:%S")
        timestamp_label.setText(current_time)

        # Log the cut action with parameters
        start_addr = start_address_input.text() or "Not set"
        dlc_val = dlc_input.text() or "Not set"
        is_checked = "Yes" if checkbox.isChecked() else "No"
        padding_val = padding_input.text() or "Not set"

        log_message = f"Cut executed at {current_time} - Start: {start_addr}, DLC: {dlc_val}, Checked: {is_checked}, Padding: {padding_val}"
        self.console_log.append(log_message)

    def delete_command_row(self, row_widget):
        """Delete a command row"""
        self.command_rows_layout.removeWidget(row_widget)
        row_widget.deleteLater()

        if hasattr(self, 'command_row_widgets'):
            if row_widget in self.command_row_widgets:
                self.command_row_widgets.remove(row_widget)

        self.console_log.append("Command row deleted")


def main():
    app = QApplication(sys.argv)
    window = MiniTool1()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()